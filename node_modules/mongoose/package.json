{"name": "mongoose", "description": "Mongoose MongoDB ODM", "version": "7.8.7", "author": "<PERSON> <<EMAIL>>", "keywords": ["mongodb", "document", "model", "schema", "database", "odm", "data", "datastore", "query", "nosql", "orm", "db"], "license": "MIT", "dependencies": {"bson": "^5.5.0", "kareem": "2.5.1", "mongodb": "5.9.2", "mpath": "0.9.0", "mquery": "5.0.0", "ms": "2.1.3", "sift": "16.0.1"}, "devDependencies": {"@babel/core": "7.23.0", "@babel/preset-env": "7.22.20", "@typescript-eslint/eslint-plugin": "5.62.0", "@typescript-eslint/parser": "5.62.0", "acquit": "1.3.0", "acquit-ignore": "0.2.1", "acquit-require": "0.1.1", "assert-browserify": "2.0.0", "axios": "1.1.3", "babel-loader": "8.2.5", "benchmark": "2.1.4", "broken-link-checker": "^0.7.8", "buffer": "^5.6.0", "cheerio": "1.0.0-rc.12", "crypto-browserify": "3.12.0", "dotenv": "16.3.1", "dox": "1.0.0", "eslint": "8.50.0", "eslint-plugin-markdown": "^3.0.0", "eslint-plugin-mocha-no-only": "1.1.1", "express": "^4.18.1", "fs-extra": "~11.1.1", "highlight.js": "11.8.0", "lodash.isequal": "4.5.0", "lodash.isequalwith": "4.4.0", "markdownlint-cli2": "^0.10.0", "marked": "4.3.0", "mkdirp": "^3.0.1", "mocha": "10.2.0", "moment": "2.x", "mongodb-memory-server": "9.5.0", "ncp": "^2.0.0", "nyc": "15.1.0", "pug": "3.0.2", "q": "1.5.1", "sinon": "16.0.0", "stream-browserify": "3.0.0", "tsd": "0.29.0", "typescript": "5.2.2", "uuid": "9.0.1", "webpack": "5.88.2"}, "directories": {"lib": "./lib/mongoose"}, "scripts": {"docs:clean": "npm run docs:clean:stable", "docs:clean:stable": "rimraf index.html && rimraf -rf ./docs/*.html  && rimraf -rf ./docs/api && rimraf -rf ./docs/tutorials/*.html && rimraf -rf ./docs/typescript/*.html && rimraf -rf ./docs/*.html && rimraf -rf ./docs/source/_docs && rimraf -rf ./tmp", "docs:clean:5x": "rimraf index.html && rimraf -rf ./docs/5.x && rimraf -rf ./docs/source/_docs && rimraf -rf ./tmp", "docs:clean:6x": "rimraf index.html && rimraf -rf ./docs/6.x && rimraf -rf ./docs/source/_docs && rimraf -rf ./tmp", "docs:copy:tmp": "mkdirp ./tmp/docs/css && mkdirp ./tmp/docs/js && mkdirp ./tmp/docs/images && mkdirp ./tmp/docs/tutorials && mkdirp ./tmp/docs/typescript && mkdirp ./tmp/docs/api && ncp ./docs/css ./tmp/docs/css --filter=.css$ && ncp ./docs/js ./tmp/docs/js --filter=.js$ && ncp ./docs/images ./tmp/docs/images && ncp ./docs/tutorials ./tmp/docs/tutorials && ncp ./docs/typescript ./tmp/docs/typescript && ncp ./docs/api ./tmp/docs/api && cp index.html ./tmp && cp docs/*.html ./tmp/docs/", "docs:copy:tmp:5x": "rimraf ./docs/5.x && ncp ./tmp ./docs/5.x", "docs:move:6x:tmp": "mv ./docs/6.x ./tmp", "docs:copy:tmp:6x": "rimraf ./docs/6.x && ncp ./tmp ./docs/6.x", "docs:checkout:gh-pages": "git checkout gh-pages", "docs:checkout:5x": "git checkout 5.x", "docs:checkout:6x": "git checkout 6.x", "docs:generate": "node ./scripts/website.js", "docs:generate:search": "node ./scripts/generateSearch.js", "docs:generate:sponsorData": "node ./scripts/loadSponsorData.js", "docs:merge:stable": "git merge master", "docs:merge:5x": "git merge 5.x", "docs:merge:6x": "git merge 6.x", "docs:test": "npm run docs:generate && npm run docs:generate:search", "docs:view": "node ./scripts/static.js", "docs:prepare:publish:stable": "npm run docs:checkout:gh-pages && npm run docs:merge:stable && npm run docs:generate && npm run docs:generate:search", "docs:prepare:publish:5x": "npm run docs:checkout:5x && npm run docs:merge:5x && npm run docs:clean:stable && npm run docs:generate && npm run docs:copy:tmp && npm run docs:checkout:gh-pages && npm run docs:copy:tmp:5x", "docs:prepare:publish:6x": "npm run docs:checkout:6x && npm run docs:merge:6x && npm run docs:clean:stable && env DOCS_DEPLOY=true npm run docs:generate && npm run docs:move:6x:tmp && npm run docs:checkout:gh-pages && npm run docs:copy:tmp:6x", "docs:prepare:publish:7x": "env DOCS_DEPLOY=true npm run docs:generate && npm run docs:checkout:gh-pages && rimraf ./docs/7.x && mv ./tmp ./docs/7.x", "docs:check-links": "blc http://127.0.0.1:8089 -ro", "lint": "eslint .", "lint-js": "eslint . --ext .js --ext .cjs", "lint-ts": "eslint . --ext .ts", "lint-md": "markdownlint-cli2 \"**/*.md\"", "build-browser": "(rm ./dist/* || true) && node ./scripts/build-browser.js", "prepublishOnly": "npm run build-browser", "release": "git pull && git push origin master --tags && npm publish", "release-5x": "git pull origin 5.x && git push origin 5.x && git push origin 5.x --tags && npm publish --tag 5x", "release-6x": "git pull origin 6.x && git push origin 6.x && git push origin 6.x --tags && npm publish --tag 6x", "mongo": "node ./tools/repl.js", "publish-7x": "npm publish --tag 7x", "test": "mocha --exit ./test/*.test.js", "test-deno": "deno run --allow-env --allow-read --allow-net --allow-run --allow-sys --allow-write ./test/deno.js", "test-rs": "START_REPLICA_SET=1 mocha --timeout 30000 --exit ./test/*.test.js", "test-tsd": "node ./test/types/check-types-filename && tsd", "tdd": "mocha ./test/*.test.js --inspect --watch --recursive --watch-files ./**/*.{js,ts}", "test-coverage": "nyc --reporter=html --reporter=text npm test", "ts-benchmark": "cd ./benchmarks/typescript/simple && npm install && npm run benchmark | node ../../../scripts/tsc-diagnostics-check"}, "main": "./index.js", "types": "./types/index.d.ts", "engines": {"node": ">=14.20.1"}, "bugs": {"url": "https://github.com/Automattic/mongoose/issues/new"}, "repository": {"type": "git", "url": "git://github.com/Automattic/mongoose.git"}, "homepage": "https://mongoosejs.com", "browser": "./dist/browser.umd.js", "config": {"mongodbMemoryServer": {"disablePostinstall": true}}, "funding": {"type": "opencollective", "url": "https://opencollective.com/mongoose"}, "tsd": {"directory": "test/types", "compilerOptions": {"esModuleInterop": false, "strict": true, "allowSyntheticDefaultImports": true, "strictPropertyInitialization": false, "noImplicitAny": false, "strictNullChecks": true, "module": "commonjs", "target": "ES2017"}}}