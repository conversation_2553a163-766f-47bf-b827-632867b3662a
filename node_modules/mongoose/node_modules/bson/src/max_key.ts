import { BSONValue } from './bson_value';

/** @public */
export interface MaxKeyExtended {
  $maxKey: 1;
}

/**
 * A class representation of the BSON MaxKey type.
 * @public
 * @category BSONType
 */
export class <PERSON><PERSON><PERSON> extends BSONValue {
  get _bsontype(): '<PERSON><PERSON><PERSON>' {
    return 'MaxKey';
  }

  /** @internal */
  toExtendedJSON(): MaxKeyExtended {
    return { $maxKey: 1 };
  }

  /** @internal */
  static fromExtendedJSON(): <PERSON><PERSON><PERSON> {
    return new MaxKey();
  }

  /** @internal */
  [Symbol.for('nodejs.util.inspect.custom')](): string {
    return this.inspect();
  }

  inspect(): string {
    return 'new MaxKey()';
  }
}
