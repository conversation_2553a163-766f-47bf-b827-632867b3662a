{"parent": "caee767b-c4ce-4448-9074-a8212c1e2643", "pid": 7506, "argv": ["/Users/<USER>/.nvm/versions/node/v18.16.0/bin/node", "/Users/<USER>/jdesboeufs/connect-mongo/node_modules/ava/lib/worker/subprocess.js"], "execArgv": [], "cwd": "/Users/<USER>/jdesboeufs/connect-mongo", "time": 1697308499298, "ppid": 7505, "coverageFilename": "/Users/<USER>/jdesboeufs/connect-mongo/.nyc_output/a45b74f9-8ef2-4938-b880-8baaf3d007ab.json", "externalId": "", "uuid": "a45b74f9-8ef2-4938-b880-8baaf3d007ab", "files": ["/Users/<USER>/jdesboeufs/connect-mongo/build/main/lib/MongoStore.spec.js", "/Users/<USER>/jdesboeufs/connect-mongo/build/main/lib/MongoStore.js", "/Users/<USER>/jdesboeufs/connect-mongo/build/main/test/testHelper.js"]}